<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Webkul Software
 *
 * @category    Webkul
 * @package     Webkul_MpGroupedProduct
 * <AUTHOR>
 * @copyright   Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license     https://store.webkul.com/license.html
 */ -->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing_data_source</item>
            <item name="deps" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">mpgrouped_product_columns</item>
    </argument>

    <dataSource name="mpgrouped_product_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">mpgrouped_product_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="cacheRequests" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
            </item>
        </argument>
        <bookmark name="bookmarks">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="saveUrl" xsi:type="url" path="mpgroupedproduct/mui_bookmark/save"/>
                        <item name="deleteUrl" xsi:type="url" path="mpgroupedproduct/mui_bookmark/delete"/>
                        <item name="namespace" xsi:type="string">mpgrouped_product_listing</item>
                    </item>
                </item>
            </argument>
        </bookmark>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.mpgrouped_product_columns</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.mpgrouped_product_listing.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">ids</item>
                </item>
            </argument>
        </filters>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">mpgrouped_product_listing.mpgrouped_product_listing.mpgrouped_product_listing.ids</item>
                </item>
            </argument>
        </paging>
    </container>
    <columns name="mpgrouped_product_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">mpgrouped_product_listing_data_source</item>
                        <item name="target" xsi:type="string">selectProduct</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">mageproduct_id</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="mageproduct_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="fieldClass" xsi:type="string">mageproduct_id</item>
                    <item name="label" xsi:type="string" translate="true"> Product Id</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>
        <column name="product_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="fieldClass" xsi:type="string">col-name</item>
                    <item name="label" xsi:type="string" translate="true">Product Name</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>
        <column name="qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="fieldClass" xsi:type="string">col-qty</item>
                    <item name="label" xsi:type="string" translate="true">Quantity</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>
        <column name="sku">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="fieldClass" xsi:type="string">col-sku</item>
                    <item name="label" xsi:type="string" translate="true">SKU</item>
                    <item name="sortOrder" xsi:type="number">65</item>
                </item>
            </argument>
        </column>
        <column name="type_id">
            <argument name="data" xsi:type="array">
               <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\Producttype</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="fieldClass" xsi:type="string">grouped-product-type_id</item>
                    <item name="label" xsi:type="string" translate="true">TYPE</item>
                    <item name="sortOrder" xsi:type="number">68</item>
                </item>
            </argument>
        </column>
        <column name="product_price" class="Magento\Catalog\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="fieldClass" xsi:type="string">col-price</item>
                    <item name="label" xsi:type="string" translate="true">Price</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
        <column name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Seller Name</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
     </columns>
</listing>

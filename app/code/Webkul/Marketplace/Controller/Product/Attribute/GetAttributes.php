<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Product\Attribute;

use Magento\Catalog\Model\ResourceModel\Product\Attribute\CollectionFactory;
use Magento\Catalog\Model\ResourceModel\Eav\Attribute;
use Webkul\Marketplace\Helper\Data as HelperData;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Webkul\Marketplace\Helper\Orders as OrdersHelper;
use Magento\Framework\App\Action\Context;
use Comave\Marketplace\Plugin\RestrictAddAttributeOption;

/**
 * Marketplace Product GetAttributes controller.
 */
class GetAttributes extends \Magento\Framework\App\Action\Action
{
    public function __construct(
        private readonly Context $context,
        private readonly CollectionFactory $attributeCollection,
        private readonly HelperData $helper,
        private readonly JsonHelper $jsonHelper,
        private readonly OrdersHelper $ordersHelper,
    ) {
        parent::__construct($context);
    }

    /**
     * Get Eav Attributes action.
     */
    public function execute()
    {
        if (!$this->helper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $ids = $this->getRequest()->getParam('attributes');

        $collection = $this->attributeCollection->create();
        $collection->addFieldToFilter('main_table.attribute_id', ['in' => $ids]);

        $attributes = [];
        foreach ($collection->getItems() as $attribute) {
            $canCreateOption = !$this->isSwatchTypeAttribute($attribute)
                && RestrictAddAttributeOption::isEditable($attribute);

            $attributes[] = [
                'id' => $attribute->getId(),
                'label' => $attribute->getFrontendLabel(),
                'code' => $attribute->getAttributeCode(),
                'options' => $attribute->getSource()->getAllOptions(false),
                'canCreateOption' => $canCreateOption
            ];
        }

        return $this->getResponse()->representJson(
            $this->jsonHelper->jsonEncode($attributes)
        );
    }

    /**
     * Check if an attribute is Swatch
     *
     * @param Attribute $attribute
     * @return bool
     */
    public function isSwatchTypeAttribute(Attribute $attribute): bool
    {
        return $this->isVisualTypeSwatch($attribute) || $this->isTextTypeSwatch($attribute);
    }

    /**
     * If attribute is visual swatch
     *
     * @param Attribute $attribute
     * @return bool
     */
    public function isVisualTypeSwatch(Attribute $attribute): bool
    {
        if (!$attribute->hasData('swatch_input_type')) {
            $this->populateAdditionalDataAttribute($attribute);
        }
        return $attribute->getData('swatch_input_type') == 'visual';
    }

    /**
     * If attribute is textual swatch
     *
     * @param Attribute $attribute
     * @return bool
     */
    public function isTextTypeSwatch(Attribute $attribute): bool
    {
        if (!$attribute->hasData('swatch_input_type')) {
            $this->populateAdditionalDataAttribute($attribute);
        }
        return $attribute->getData('swatch_input_type') == 'text';
    }

    /**
     * Function populateAdditionalDataAttribute
     *
     * @param Attribute $attribute
     * @return $this
     */
    private function populateAdditionalDataAttribute(Attribute $attribute): Attribute
    {
        $attrAdditionalData = $this->ordersHelper->getProductOptions(
            $attribute->getData('additional_data')
        );
        if (!empty($attrAdditionalData) && is_array($attrAdditionalData)) {
            $attributeAdditionalDataKeys = [
                'swatch_input_type',
                'update_product_preview_image',
                'use_product_image_for_swatch'
            ];
            foreach ($attributeAdditionalDataKeys as $key) {
                if (!empty($attrAdditionalData[$key])) {
                    $attribute->setData($key, $attrAdditionalData[$key]);
                }
            }
        }
        return $attribute;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">marketplace_products_listing.marketplace_products_listing_data_source</item>
            <item name="deps" xsi:type="string">marketplace_products_listing.marketplace_products_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">products_columns</item>
    </argument>
    <dataSource name="marketplace_products_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Webkul\Marketplace\Ui\DataProvider\ProductListDataProvider</argument>
            <argument name="name" xsi:type="string">marketplace_products_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="cacheRequests" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="saveUrl" xsi:type="url" path="marketplace/mui_bookmark/save"/>
                        <item name="deleteUrl" xsi:type="url" path="marketplace/mui_bookmark/delete"/>
                        <item name="namespace" xsi:type="string">marketplace_products_listing</item>
                    </item>
                </item>
            </argument>
        </bookmark>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="statefull" xsi:type="array">
                        <item name="applied" xsi:type="boolean">false</item>
                    </item>
                    <item name="params" xsi:type="array">
                        <item name="filters_modifier" xsi:type="array" />
                    </item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">marketplace_products_listing.marketplace_products_listing.products_columns.productIds</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                </item>
            </argument>
            <action name="massdelete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">massdelete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="marketplace/product_ui/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected products?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="products_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/listing</item>
                <item name="editorConfig" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">marketplace_products_listing.marketplace_products_listing.products_columns.productIds</item>
                    <item name="enabled" xsi:type="boolean">true</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="clientConfig" xsi:type="array">
                        <item name="saveUrl" xsi:type="url" path="marketplace/product/inlineEdit"/>
                        <item name="validateBeforeSave" xsi:type="boolean">false</item>
                    </item>
                </item>
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">marketplace_products_listing.marketplace_products_listing.products_columns_editor</item>
                        <item name="target" xsi:type="string">startEdit</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                            <item name="1" xsi:type="boolean">true</item>
                        </item>
                    </item>
                </item>

            </item>
        </argument>
        <selectionsColumn name="productIds">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="entity_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>
        <column name="thumbnail" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\Thumbnail">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/thumbnail</item>
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="altField" xsi:type="string">name</item>
                    <item name="has_preview" xsi:type="string">1</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">Thumbnail</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
            </argument>
        </column>
        <column name="name" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\ProductUrl">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="label" xsi:type="string" translate="true">Name</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>
        <column name="category_id" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\Category">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\CategoryList</item>
                <item name="config" xsi:type="array">
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="label" xsi:type="string" translate="true">Categories</item>
                    <item name="sortOrder" xsi:type="number">35</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="filter" xsi:type="string">select</item>
                </item>
            </argument>
        </column>
        <column name="attribute_set_id">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\AttributeSets</item>
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="filter" xsi:type="string">select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Attribute Set</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>
        <column name="is_approved">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\ProductStatus</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/selectLink</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">select</item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="status">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\ProductListStatus</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/selectLink</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Product Status</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">select</item>
                    </item>

                </item>
            </argument>
        </column>
        <column name="type_id">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Webkul\Marketplace\Model\Product\Source\Producttype</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Type</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>
        <column name="sku">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">SKU</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
        <column name="qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">qty</item>
                    <item name="label" xsi:type="string" translate="true">Qty</item>
                    <item name="sortOrder" xsi:type="number">75</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="validate-zero-or-greater" xsi:type="boolean">true</item>
                        </item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="qtySold" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\QtySold">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Sold</item>
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/link</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="filterable" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">76</item>
                </item>
            </argument>
        </column>
        <column name="qtyConfirmed" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\QtyConfirmed">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Confirmed</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="filterable" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">77</item>
                </item>
            </argument>
        </column>
        <column name="qtyPending" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\QtyPending">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Pending</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="filterable" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">78</item>
                </item>
            </argument>
        </column>
        <column name="price" class="Magento\Catalog\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="label" xsi:type="string" translate="true">Price</item>
                    <item name="sortOrder" xsi:type="number">80</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">price</item>
                        <item name="validation" xsi:type="array">
                            <item name="validate-zero-or-greater" xsi:type="boolean">true</item>
                        </item>
                    </item>

                </item>
            </argument>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Created At</item>
                    <item name="sortOrder" xsi:type="number">90</item>
                </item>
            </argument>
        </column>
        <column name="visibility">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Catalog\Model\Product\Visibility</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Visibility</item>
                    <item name="sortOrder" xsi:type="number">100</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">select</item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="websites" class="Magento\Catalog\Ui\Component\Listing\Columns\Websites">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Store\Model\ResourceModel\Website\Collection</item>
                <item name="config" xsi:type="array">
                    <item name="add_field" xsi:type="boolean">true</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Websites</item>
                    <item name="sortOrder" xsi:type="number">110</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="proactions" class="Webkul\Marketplace\Ui\Component\Listing\Columns\Frontend\ProductAction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/columns/actionlink</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">Action</item>
                    <item name="sortOrder" xsi:type="number">120</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>

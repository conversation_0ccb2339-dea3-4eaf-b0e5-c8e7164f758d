<?php
/**
 * @category   Webkul
 * @package    Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
namespace Webkul\MpMultiShopifyStoreMageConnect\Controller\Adminhtml\ShopifyAccount;

use Magento\Framework\Event\ManagerInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Controller\Adminhtml\ShopifyAccount;
use Magento\Framework\Controller\ResultFactory;

class Save extends ShopifyAccount
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    private $resultPageFactory;

    /**
     * @var /Webkul\MpMultiShopifyStoreMageConnect\Model/ShopifyaccountsFactory
     */
    private $shopifyAccountsFactory;

    /**
     * @var \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    private $encryptor;

     /**
      * Construct function
      *
      * @param \Magento\Backend\App\Action\Context $context
      * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
      * @param \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyAccountsFactory
      * @param \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $helper
      * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
      */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Webkul\MpMultiShopifyStoreMageConnect\Model\ShopifyaccountsFactory $shopifyAccountsFactory,
        \Webkul\MpMultiShopifyStoreMageConnect\Helper\Data $helper,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        private readonly ManagerInterface $eventManager,
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->shopifyAccountsFactory = $shopifyAccountsFactory;
        $this->dataHelper = $helper;
        $this->encryptor = $encryptor;
    }

    /**
     * Shopify account details
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $flag = false;
        $reserveId = 0;
        $resultRedirect = $this->resultRedirectFactory->create();
        $temp = $parameters = $this->getRequest()->getParams();
        if ($temp['shopify_app_select']==1) {
            $parameters['access_token'] = $this->beforeSave($parameters['access_token'])['value'];
        } else {
            $parameters['shopify_api_key'] = $this->beforeSave($parameters['shopify_api_key'])['value'];
            $parameters['shopify_pwd'] = $this->beforeSave($parameters['shopify_pwd'])['value'];
        }

        if ($this->getRequest()->getParam('id')) {
            if ($temp['shopify_app_select']==1) {
                $isAccessTokenChange = $this->beforeSave($temp['access_token'])['status'];
                if (!$isAccessTokenChange) {
                    $temp['access_token'] = $this->getAccessTokenFromDbToVerify($this->getRequest()->getParam('id'));
                } else {
                    $temp['access_token'] = $parameters['access_token'];
                }
                $temp['access_token'] = $this->encryptor->decrypt($temp['access_token']);
            } else {
                $isApiKeyChange = $this->beforeSave($temp['shopify_api_key'])['status'];
                $isApiPwdChange = $this->beforeSave($temp['shopify_pwd'])['status'];
                if (!$isApiKeyChange) {
                    $temp['shopify_api_key'] = $this->getTheApiKeyFromDbToVerify($this->getRequest()->getParam('id'));
                } else {
                    $temp['shopify_api_key'] = $this->encryptor->decrypt($parameters['shopify_api_key']);
                }
                if (!$isApiPwdChange) {
                    $temp['shopify_pwd'] = $this->getTheApiPwdFromDbToVerify($this->getRequest()->getParam('id'));
                } else {
                    $temp['shopify_pwd'] = $this->encryptor->decrypt($parameters['shopify_pwd']);
                }
                $temp['shopify_api_key'] = $this->encryptor->decrypt($temp['shopify_api_key']);
                $temp['shopify_pwd'] = $this->encryptor->decrypt($temp['shopify_pwd']);
            }
        }
        if ($temp['shopify_app_select']==1) {
            $status = $this->checkForAccessTokenEmptyValue($parameters);
        } else {
            $status = $this->checkForEmptyValue($parameters);
        }
        $emptyValueCheck = ['status'=>'', 'msg'=> ''];
        if ($status['status']) {
            $data = $this->dataHelper->authorizeShopifyShop($temp);
            $data['magento_base_currency'] = $this->dataHelper->getBaseCurrencyCode();
            $error = __('Shopify user didn\'t authorize successfully, Please try again.');
            if ($data['status'] && $data['api_request_http_code'] == 200) {
                if (isset($data['product_type_allowed']) && $data['product_type_allowed']) {
                    $data['product_type_allowed'] = is_array($data['product_type_allowed']) ?
                        implode(',', $data['product_type_allowed']) :
                        $data['product_type_allowed'];
                }


                $data['access_token'] = $this->encryptor->encrypt($data['access_token']);
                $model = $shopifyAccountsCollection = $this->shopifyAccountsFactory->create();
                $model->addData($data)->save();
                $this->eventManager->dispatch(
                    'shopify_register_success',
                    [
                        'account_id' => $model->getId()
                    ]
                );
                $this->messageManager
                    ->addSuccess(
                        __('Shopify details saved successfully, Now edit the record for syncronization process')
                    );
                if ($model->getId() && isset($temp['back'])) {
                    return $this->resultFactory->create(
                        ResultFactory::TYPE_REDIRECT
                    )->setPath('*/*/edit', ['id'=> $model->getId()]);
                } else {
                    return $this->resultFactory->create(
                        ResultFactory::TYPE_REDIRECT
                    )->setPath('*/*/');
                }
            } else {
                $this->messageManager->addError($data['error_msg']);
                return $this->resultFactory->create(
                    ResultFactory::TYPE_REDIRECT
                )->setPath('*/*/');
            }
        } else {
            $this->messageManager->addError(__('Something went wrong'));
            return $this->resultFactory->create(
                ResultFactory::TYPE_REDIRECT
            )->setPath('*/*/');
        }
    }

    /**
     * GetTheApiKeyFromDbToVerify function get the api key
     *
     * @param int $id
     * @return string
     */
    private function getTheApiKeyFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getShopifyApiKey();
    }

     /**
      * GetAccessTokenFromDbToVerify function get the api key
      *
      * @param int $id
      * @return string
      */
    private function getAccessTokenFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getAccessToken();
    }

    /**
     * GetTheApiPwdFromDbToVerify function get the api password
     *
     * @param int $id
     * @return string
     */
    private function getTheApiPwdFromDbToVerify($id = '')
    {
        $modal = $this->shopifyAccountsFactory->create()->load($id);
        return $modal->getShopifyPwd();
    }

    /**
     * BeforeSave function check that value is obscure or not
     *
     * @param string $value
     * @return array
     */
    private function beforeSave($value)
    {
        if (!preg_match('/^\*+$/', $value) && !empty($value)) {
            $encrypted = $this->encryptor->encrypt($value);
            return ['value' => $encrypted, 'status' => true];
        } elseif (empty($value)) {
            return ['value' => $value, 'status' => false];
        }
        return ['value' => $value, 'status' => false];
    }

    /**
     * CheckForAccessTokenEmptyValue function check for empty value
     *
     * @param array $parameters
     * @return array
     */
    private function checkForAccessTokenEmptyValue($parameters)
    {
        if ($parameters['access_token'] == '' ||
            $parameters['shopify_domain_name'] == ''
        ) {
            return ['status' => 0, 'msg'=>'requird fields are empty'];
        }
        return ['status'=> 1, 'msg'=>''];
    }

     /**
      * CheckForEmptyValue function check for empty value
      *
      * @param array $parameters
      * @return array
      */
    private function checkForEmptyValue($parameters)
    {
        if ($parameters['shopify_api_key'] == '' ||
            $parameters['shopify_pwd'] == '' ||
            $parameters['shopify_domain_name'] == ''
        ) {
            return ['status' => 0, 'msg'=>'requird fields are empty'];
        }
        return ['status'=> 1, 'msg'=>''];
    }
}

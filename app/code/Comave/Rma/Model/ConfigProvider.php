<?php

declare(strict_types=1);

namespace Comave\Rma\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider
{
    private const string COMAVE_XML_RMA_LOCAL_GRACE_PERIOD =
        'comave_rma/general/grace_period_local';
    private const string COMAVE_XML_RMA_LOCAL_GRACE_INTERNATIONAL =
        'comave_rma/general/grace_period_international';
    private const string COMAVE_XML_RMA_EXCLUDED_CATEGORIES =
        'comave_rma/general/excluded_categories';
    private const string COMAVE_XML_RMA_STATUS_MAPPING =
        'comave_rma/general/status_mapping';
    private const string COMAVE_XML_RMA_EMAIL_NOTIFICATION_ENABLED =
        'comave_rma/general/enable_email_notification';
    private const string COMAVE_XML_RMA_EMAIL_NOTIFICATION_TEMPLATE =
        'comave_rma/general/email_notification_template';

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(private readonly ScopeConfigInterface $scopeConfig)
    {
    }

    /**
     * @param int|null $websiteId
     * @return string|null
     */
    public function getEmailNotificationTemplate(?int $websiteId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::COMAVE_XML_RMA_EMAIL_NOTIFICATION_TEMPLATE,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );
    }

    /**
     * @param int|null $websiteId
     * @return bool
     */
    public function isEmailNotificationEnabled(?int $websiteId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::COMAVE_XML_RMA_EMAIL_NOTIFICATION_ENABLED,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );
    }

    /**
     * @param int|null $websiteId
     * @return int|null
     */
    public function getInternationalGracePeriod(?int $websiteId = null): ?int
    {
        $value = $this->scopeConfig->getValue(
            self::COMAVE_XML_RMA_LOCAL_GRACE_INTERNATIONAL,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );

        return !empty($value) ? (int) $value : null;
    }

    /**
     * @param int|null $websiteId
     * @return int|null
     */
    public function getLocalGracePeriod(?int $websiteId = null): ?int
    {
        $value = $this->scopeConfig->getValue(
            self::COMAVE_XML_RMA_LOCAL_GRACE_PERIOD,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );

        return !empty($value) ? (int) $value : null;
    }

    /**
     * @param int|null $websiteId
     * @return array|null
     */
    public function getExcludedCategories(?int $websiteId = null): ?array
    {
        $value = $this->scopeConfig->getValue(
            self::COMAVE_XML_RMA_EXCLUDED_CATEGORIES,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );

        return json_decode($value ?: '{}', true) ?? null;
    }

    /**
     * @return array{enabled: string, check_unpaid_installments: string, days: string, text: string}[]
     */
    public function getStatusMapping(?int $websiteId = null): array
    {
        /** @var string|null $rawValue */
        $rawValue  = $this->scopeConfig->getValue(
            self::COMAVE_XML_RMA_STATUS_MAPPING,
            ScopeInterface::SCOPE_WEBSITES,
            $websiteId
        );

        if (!$rawValue) {
            return [];
        }

        /** @var mixed[] $array */
        $array = json_decode($rawValue, true);

        /** @var array<array{rma_status: string, comave_status: string}> $values */
        $values = array_values($array);

        return $values;
    }
}

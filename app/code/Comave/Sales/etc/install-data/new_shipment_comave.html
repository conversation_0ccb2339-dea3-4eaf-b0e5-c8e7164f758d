{{template config_path="design/email/header_template"}}
<p>
    {{trans "****This is an automatically generated email, please do not reply****"}}
</p>
<div style="padding: 0 10px 0 10px;">

    <table align="center" style="display: block;  text-align:center; width: 660px;">
        <tbody style="display: block">
        <tr>
            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
                <p class="greeting"  style="padding-top:10px ;padding-left:20px;">{{trans "Hello %name," name=$order_data.customer_name}}</p>
            </td>
        </tr>
        </tbody>
    </table>
    <table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
        <tbody>
        <tr>
            <td style="margin-left: 0px">
                <p style="padding-left:20px;">
                    {{trans 'Thank you for shopping with us. We would like to confirm that your item has been shipped. Your order and shipping details are mentioned below.'}}
                </p>
            </td>
        </tr>
        <tr>
            <td style="margin-left: 0px">
                <p style="padding-left:20px;">
                    {{layout handle="sales_email_order_shipment_track" shipment_id=$shipment_id order_id=$order_id}}
                </p>
            </td>
        </tr>
        <tr>
            <td style="margin-left: 0px">
                <p style="padding-left:20px;">
                    {{trans 'To know more about the order, payment and shipping details, please visit <a href="%account_url">My Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
                </p>
            </td>
        </tr>

        <tr style="display: block">
            <td class="dark"  style="display: block; ">
                <h3 style="text-align: left; letter-spacing: 0.025em;padding-left:20px;">
                    {{trans "ORDER NUMBER: %order_id" order_id=$order.increment_id}}
                </h3>
            </td>
        </tr>
        <tr>
            <td align="center" style="">
                <h2 style="text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;">
                    {{trans 'ComAve shipment id:  #%shipment_id' shipment_id=$shipment.increment_id}}
                </h2>
            </td>
        </tr>
        </tbody>
    </table>

    <table style="width: 660px">
        <tr class="email-information">
            <td>
                <table class="order-details" style="border-top: 5px solid #000000">
                    <tr>
                        <td class="address-details" style="padding-top: 30px !important;padding-left:20px;">
                            <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
                            <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="address-details" style="padding-top: 30px !important">
                            <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
                            <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
                        </td>
                        {{/depend}}
                    </tr>
                    <tr>
                        <td class="method-info wp-method-info" style="padding-bottom: 40px !important;padding-left:20px;">
                            <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
                            {{var payment_html|raw}}
                        </td>
                        {{depend order_data.is_not_virtual}}
                        <td class="method-info" style="padding-bottom: 40px !important">
                            <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
                            <p style="color: #555656;">{{var order.shipping_description}}</p>
                            {{if shipping_msg}}
                            <p style="color: #555656;">{{var shipping_msg}}</p>
                            {{/if}}
                        </td>
                        {{/depend}}
                    </tr>
                </table>
                {{depend comment}}
                <table class="message-info">
                    <tr>
                        <td>
                            {{var comment|escape|nl2br}}
                        </td>
                    </tr>
                </table>
                {{/depend}}

                {{layout handle="weltpixel_sales_email_order_shipment_items" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}
            </td>
        </tr>
        <tr>
            <td colspan="2" align="center">
                <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody style="display: block">
                    <tr style="display: block">
                        <td style="display: block">
                            <table class="inner-wrapper"  cellspacing="0" cellpadding="0" align="center" width="100%">
                                <tr>
                                    <td align="center" style="padding: 8px 0 !important">
                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold; border:unset !important;">{{trans "VIEW ORDER"}}</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td style="margin-left: 0px">
                <p style="margin: 10px 0 !important; padding-left:20px;">
                    {{trans 'If you need further assistance with you order please contact us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
                </p>
            </td>
        </tr>
    </table>
</div>

{{template config_path="design/email/footer_template"}}

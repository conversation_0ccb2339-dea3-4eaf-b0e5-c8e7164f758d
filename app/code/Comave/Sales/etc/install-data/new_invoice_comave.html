{{template config_path="design/email/header_template"}}{{layout handle="preheader_section" area="frontend"}}
<p>
          {{trans "****This is an automatically generated email, please do not reply****"}}
 </p>
<div style="padding: 0 10px 0 10px;">
<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
<tbody>
 <tr>
<td align="left">
            <p class="greeting"  style="padding-top:10px ;padding-left:20px;">{{trans "Hello %name," name=$order_data.customer_name}}</p>
 </td>
</tr>

 <td>
            <p style="margin: 10px 0 !important; margin-left:20px; margin-right: 2px !important; padding-left: 20px;">
          {{trans 'Thank you for shopping with ComAve. We have successfully placed your order. You will get a confirmation once your item has been shipped. All your order details are mentioned below. If you would like to view the order status or download the invoice please visit <a href="%account_url">Your Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
 </p>


<!--<p style="margin: 10px 0 !important; padding-left:20px;">
                {{trans 'You can view the entire status of your order by checking <a href="%account_url">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
            </p>-->
        </td>
    </tr>

  <tr style="display:block;">
        <td class="dark" align="left" style="display:block; ">
            <h3 style="text-align: left; letter-spacing: 0.025em;padding-left:20px;">
                {{trans "ORDER NUMBER: %increment_id." increment_id=$order.increment_id |raw}}
            </h3>
        </td>
    </tr>
 <tr>
        <td align="left" style="">
            <h2 style="text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;">
                {{trans 'Your Invoice: #%invoice_id' invoice_id=$invoice.increment_id |raw}}
            </h2>
        </td>
    </tr>
    </tbody>
</table>

<table style="width: 660px">
    <tr class="email-information">
        <td>


            <table class="order-details" style="border-top: 5px solid #000000">
                <tr>
                   <td class="address-details" style="padding-top: 29px !important; padding-left:20px;">
                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="address-details" style="padding-top: 29px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                   <td class="method-info wp-method-info" style="padding-bottom: 40px !important;padding-left:20px;">
                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    {{depend order_data.is_not_virtual}}
                    <td class="method-info" style="padding-bottom: 40px !important">
                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
                        <p style="color: #555656;">{{var order.shipping_description}}</p>
                        {{if shipping_msg}}
                        <p style="color: #555656;">{{var shipping_msg}}</p>
                        {{/if}}
                    </td>
                    {{/depend}}
                </tr>
            </table>
           {{depend order_data.email_customer_note}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order_data.email_customer_note|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}

            {{layout handle="weltpixel_sales_email_order_invoice_items" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area="frontend"}}
        </td>
    </tr>
    <tr>
        <td colspan="2" align="center">
            <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
                <tbody style="display:block;">
                    <tr style="display: block">
                        <td style="display: block">
                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
                                <tr>
                                    <td align="center" style="padding: 8px 0 !important">
                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </td>
    </tr>
 <tr>
        <td style="margin-left: 0px">
            <p style="margin: 10px 0 !important; padding-left:20px;">
                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
            </p>
        </td>
 </tr>
<tr>

 <tr>
        <!--<td colspan="2" style="padding-top: 35px;padding-left:20px;">
            {{block class="Magento\Cms\Block\Block" area="frontend" block_id="weltpixel_custom_block_returns"}}
        </td>-->
    </tr>

</table>
</div>
{{template config_path="design/email/footer_template"}}

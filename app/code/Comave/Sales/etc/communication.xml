<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    <topic name="sales.place-order.post-processing.reward.topic"
           request="Comave\Sales\Api\PostProcessingMessageInterface">
        <handler name="sales.place-order.post-processing.reward.topic.handler"
                 type="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing"
                 method="execute"/>
    </topic>
    <topic name="sales.place-order.post-processing.seller-email.topic"
           request="Comave\Sales\Api\PostProcessingMessageInterface">
        <handler name="sales.place-order.post-processing.seller-email.topic.handler"
                 type="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing"
                 method="execute"/>
    </topic>
</config>
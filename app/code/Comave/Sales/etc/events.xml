<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_submit_all_after">
        <observer name="comave_sales_observer_checkout_submit_all_after"
                  instance="Comave\Sales\Observer\CheckoutSubmitAllAfter"/>
    </event>
    <event name="checkout_submit_before">
        <observer name="preventMultipleItemsInCartBeforeOrder"
                instance="Comave\Sales\Observer\PreventMultipleSellerItems"/>
    </event>
    <event name="email_order_set_template_vars_before">
        <observer name="comave_sales_add_frontend_base_url"
                  instance="Comave\Sales\Observer\EmailTemplateVarsObserver" />
    </event>

    <event name="email_invoice_set_template_vars_before">
        <observer name="comave_sales_add_frontend_base_url"
                  instance="Comave\Sales\Observer\EmailTemplateVarsObserver" />
    </event>

    <event name="email_shipment_set_template_vars_before">
        <observer name="comave_sales_add_frontend_base_url"
                  instance="Comave\Sales\Observer\EmailTemplateVarsObserver" />
    </event>

    <event name="email_order_comment_set_template_vars_before">
        <observer name="comave_sales_add_frontend_base_url"
                  instance="Comave\Sales\Observer\EmailTemplateVarsObserver" />
    </event>

     <event name="email_creditmemo_set_template_vars_before">
        <observer name="comave_sales_add_frontend_base_url"
                  instance="Comave\Sales\Observer\EmailTemplateVarsObserver" />
    </event>
</config>

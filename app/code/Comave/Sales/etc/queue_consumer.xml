<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="sales.place-order.post-processing.reward"
              queue="sales.place-order.post-processing.reward"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing::execute"/>
    <consumer name="sales.place-order.post-processing.seller-email"
              queue="sales.place-order.post-processing.seller-email"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing::execute"/>
    <consumer name="sales.place-order.post-processing.experience"
              queue="sales.place-order.post-processing.experience"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing::execute"/>
</config>
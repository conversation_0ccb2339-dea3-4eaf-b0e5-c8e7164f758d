<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/publisher.xsd">
    <publisher topic="sales.place-order.post-processing.reward.topic">
        <connection name="amqp" exchange="sales.place-order.post-processing.exchange"/>
    </publisher>
    <publisher topic="sales.place-order.post-processing.seller-email.topic">
        <connection name="amqp" exchange="sales.place-order.post-processing.exchange"/>
    </publisher>
</config>
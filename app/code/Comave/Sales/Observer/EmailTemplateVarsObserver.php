<?php

declare(strict_types=1);

namespace Comave\Sales\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\DeploymentConfig;

class EmailTemplateVarsObserver implements ObserverInterface
{
    public function __construct(
        private readonly DeploymentConfig $deploymentConfig
    ) {
    }

    /**
     * Add frontend_base_url from env.php to email template variables
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $transport = $observer->getEvent()->getTransport();
        
        if ($transport) {
            $frontendBaseUrl = $this->deploymentConfig->get('frontend_base_url');
            
            if ($frontendBaseUrl) {
                $transport->setData('frontend_base_url', rtrim($frontendBaseUrl, '/'));
            }
        }
    }
}

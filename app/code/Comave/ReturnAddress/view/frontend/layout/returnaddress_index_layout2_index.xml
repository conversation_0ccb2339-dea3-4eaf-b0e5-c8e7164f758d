<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
    </head>
    <body>
        <referenceContainer name="seller.content">
            <block class="Magento\Framework\View\Element\Template" name="comave_returnaddress_manage" template="Comave_ReturnAddress::account/list.phtml" cacheable="false"></block>
        </referenceContainer>
        <referenceContainer name="comave_returnaddress_manage">
            <uiComponent name="comave_returnaddress_list_front"/>
        </referenceContainer>
    </body>
</page>

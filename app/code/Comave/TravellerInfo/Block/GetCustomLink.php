<?php
namespace Comave\TravellerInfo\Block;

use Comave\TravellerInfo\Model\TravellerInfoFactory as TravFormModel;
use Magento\Reward\Model\RewardFactory;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Customer\Model\CustomerFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
// use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;

class GetCustomLink extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

     protected $_orderCollectionFactory;
    /**
     * @var TravFormModel
     */
    protected $travFormModel;

    protected $currencyModel;
   /**
    * Construct
    *
    * @param \Magento\Customer\Model\Session $customerSession
    * @param TravFormModel $travFormModel
    * @param array $data
    */
    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\App\Http\Context $httpContext,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        CollectionFactory $orderCollectionFactory,
        TravFormModel $travFormModel,
        RewardFactory $rewardFactory,
        CustomerFactory $customerFactory,
        CollectionFactory $rewardHistoryCollectionFactory,
        \Comave\LixApiConnector\Helper\Data $dataHelper,
        \Comave\LixApiConnector\Block\RewardBalance $rewardblockObj,
        \Webkul\Marketplace\Model\SaleslistFactory $saleslistFactory,
        \Webkul\Marketplace\Model\SellerFactory $sellerFactory,
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellerlistCollectionFactory,
        OrderRepositoryInterface $orderRepository,
        \Magento\Sales\Model\ResourceModel\Order\Status\CollectionFactory $statusCollectionFactory,
        // \Magento\Sales\Model\ResourceModel\Order\CollectionFactory $orderCollectionFactory,
        array $data = []
    ) {
        $this->_storeManager = $storeManager;
        $this->_customerSession = $customerSession;
        $this->httpContext = $httpContext;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->travFormModel = $travFormModel;
        $this->_rewardFactory = $rewardFactory;
        $this->customerFactory = $customerFactory;
        $this->rewardHistoryCollectionFactory = $rewardHistoryCollectionFactory;
        $this->dataHelper = $dataHelper;
        $this->rewardblockObj = $rewardblockObj;
        $this->saleslistFactory = $saleslistFactory;
        $this->sellerFactory = $sellerFactory;
        $this->_sellerlistCollectionFactory = $sellerlistCollectionFactory;
        $this->orderRepository = $orderRepository;
        $this->statusCollectionFactory = $statusCollectionFactory;
        // $this->_orderCollectionFactory = $orderCollectionFactory;
        parent::__construct($context, $data);

    }

    public function isCustomerLoggedIn()
    {
        return (bool)$this->httpContext->getValue(\Magento\Customer\Model\Context::CONTEXT_AUTH);
    }

    public function isCustLoggedIn()
    {
        return $this->_customerSession->isLoggedIn();
    }

    public function getCustId()
	{
    	return $this->httpContext->getValue('customer_id');
	}

	public function getCustomerName()
	{
    	return $this->httpContext->getValue('customer_name');
	}

	public function getCustomerEmail()
	{
    	return $this->httpContext->getValue('customer_email');
	}

    public function getReturnWebsiteId()
    {
        $customer = $this->_customerRepositoryInterface->getById($this->getCustId());
        $websiteId = $customer->getWebsiteId();

        return $websiteId;
    }

    // public function getCustomerId(){
    //     $customer = array();
    //     $custId = $this->_customerSession->getCustomer()->getId();
    //     $custName = $this->_customerSession->getCustomer()->getFirstname().' '.$this->_customerSession->getCustomer()->getLastname();
    //     $custEmail = $this->_customerSession->getCustomer()->getEmail();

    //     $customer['id'] = $custId;
    //     $customer['name'] = $custName;
    //     $customer['email'] = $custEmail;

    //     return $customer;
    // }

    public function getRefundedForms()
    {
        $customerId = $this->getCustId();
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        )->addFieldToFilter(
            'status',
            ['eq' => 'FORM_REFUNDED']
        );

        return $collection;
    }

    public function getTotalForm(){
        $customerId = $this->getCustId();
        $collection = $this->travFormModel->create()->getCollection()
        ->addFieldToFilter(
            'traveller_id',
            ['eq' => $customerId]
        );

        $count = $collection->count();

        return $count;
    }

    public function getRewardPoint()
    {
        if($this->isCustLoggedIn()){
            $customer = $this->_customerRepositoryInterface->getById($this->getCustId());
            $custEmail = $customer->getEmail();

            $custId = $customer->getId();

            $websiteId = $customer->getWebsiteId();
            $reward = $this->_rewardFactory->create()->setCustomer($customer);
            $reward->setWebsiteId($websiteId);
            $reward->loadByCustomer();
            $lixWallet = 'lix_wallet';
            $lixCurrResp = $this->dataHelper->getCustLixWallet($this->getCustId(),$lixWallet);


            $lixBalResp =$this->dataHelper->getLixBalanceByEmail($custEmail,$lixCurrResp);


            $getPointBalance = $reward->getPointsBalance();

            if(!empty($lixBalResp)){
                if(array_key_exists("data", $lixBalResp)){
                    $lixBalance = $lixBalResp['data']['balance'];
                    if ($getPointBalance == 0 ) {
                        $reward = $this->rewardblockObj->getRewardPointBalance()->setCustomer($customer)
                                    ->setCustomerId($customer->getId())
                                    ->setWebsiteId($websiteId)
                                    // ->loadByCustomer()
                                    ->setPointsBalance($lixBalance)
                                    ->setAction(1)
                                    ->setComment(__('Additional Information Reward'))
                                    ->updateRewardPoints();
                    }
                        $getPointBalance = $reward->getPointsBalance();
                }
            }

            return $getPointBalance;
        }
        return null;
    }

    public function getPoints()
    {
        if($this->isCustLoggedIn()){
            $customer = $this->_customerRepositoryInterface->getById($this->getCustId());
            $websiteId = $customer->getWebsiteId();
            $reward = $this->_rewardFactory->create()->setCustomer($customer);
            $reward->setWebsiteId($websiteId);
            $reward->loadByCustomer();
            $pointsBalance = $reward->getCurrencyAmount();

            return $pointsBalance;
        }
        return null;

    }

    public function getFanId()
    {
        $customer = $this->_customerRepositoryInterface->getById($this->getCustId());
        if($customer->getCustomAttribute('club_followers_no')){
        $cattrValue = $customer->getCustomAttribute('club_followers_no')->getValue();

        return $cattrValue;
       }
    }

    public function getMediaUrl()
    {
         $mediaUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA );
         return $mediaUrl;
    }

    public function getCustomerOrderCount($customerId)
    {
        $orderCollection = $this->orderCollectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId)
            ->addFieldToFilter('status', 'pre_transit');

        return $orderCollection->getSize();
    }

    public function getCustCompleteOrderCount($customerId)
    {
        $orderCollection = $this->orderCollectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId)
            ->addFieldToFilter('status', 'delivered');

        return $orderCollection->getSize();
    }

    public function getCustomerTotalOrderCount($customerId)
    {
        $orderCollection = $this->orderCollectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId)
            ->addFieldToFilter('status', 'processing');

        return $orderCollection->getSize();
    }

    public function getLixWallet()
    {
        if($this->isCustLoggedIn()){
            $customer = $this->_customerRepositoryInterface->getById($this->getCustId());
            if($customer->getCustomAttribute('lix_wallet')){
            $walletValue = $customer->getCustomAttribute('lix_wallet')->getValue();
            return $walletValue;
           }
       }
       return null;
    }

    public function getSellerInfo($id, $order)
    {
        $storeCollection = array();

        $sellerId = 0;
        $orderId = $order->getId();

        $marketplaceSalesCollection =  $this->saleslistFactory->create()
            ->getCollection()
            ->addFieldToFilter(
                'mageproduct_id',
                ['eq' => $id]
            )
            ->addFieldToFilter(
                'order_id',
                ['eq' => $orderId]
            );

        if (count($marketplaceSalesCollection)) {
            foreach ($marketplaceSalesCollection as $mpSales) {
                $sellerId = $mpSales->getSellerId();

                $storeCollection = $this->_sellerlistCollectionFactory->create()
                ->addFieldToSelect(
                    '*'
                )
                ->addFieldToFilter(
                    'seller_id',
                    ['eq' => $sellerId]
                )
                ->addFieldToFilter(
                    'is_seller',
                    ['eq' => 1]
                );

                // $seller = $this->sellerFactory->create()->load($sellerId);
                // $sellerName = $seller->getName();
                // $logger->info(print_r($sellerName,true));
                // // Now $sellerName contains the name of the seller associated with the given seller ID.
                 return $storeCollection;

            }
        }
        return $storeCollection;
    }

    public function getBaseUrl()
    {
        $baseUrl = $this->_storeManager->getStore()->getBaseUrl();
        return $baseUrl;
    }

    public function getOrderById($orderId)
    {
        // Load order by ID
        $order = $this->orderRepository->get($orderId);

        // Get all items
        $items = $order->getAllItems();

        return $items;
    }

    public function getOrderStatusByStateAndWebsite($orderState)
    {
        // Get the current store
        $currentStore = $this->_storeManager->getStore();

        // Get order status collection
        $statusCollection = $this->statusCollectionFactory->create();

        // Filter order statuses based on the current website and order state
        $statusCollection->addVisibleFilter()
            ->addFieldToFilter('website_ids', $currentStore->getWebsiteId())
            ->addFieldToFilter('state', $orderState);

        // Retrieve the order status
        $orderStatus = '';
        foreach ($statusCollection as $status) {
            $orderStatus = $status->getStatus();
            break; // Assuming there's only one status for a state on a website
        }
        return $orderStatus;
    }

    public function getClubSellerInfo()
    {
        $customerCollection = $this->customerFactory->create()->getCollection()
                            ->addAttributeToSelect(['wkv_club_unique_identfier', 'wkv_club_logo', 'wkv_club_name'])
                            ->setOrder('wkv_club_name', 'ASC')
                            ->load();

        $customerArray = [];
        foreach ($customerCollection as $customer) {
            $customerArray[] = $customer->getData();
        }

        return $customerArray;
    }

    public function getOrderWebsiteName($orderId)
    {
        $order = $this->orderRepository->get($orderId);
        $storeId = $order->getStoreId();
        $websiteId = $this->_storeManager->getStore($storeId)->getWebsiteId();
        $website = $this->_storeManager->getWebsite($websiteId);
        return $website->getName();
    }

}

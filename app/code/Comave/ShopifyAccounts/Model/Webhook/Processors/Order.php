<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Webhook\Processors;

use Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface;
use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Comave\ShopifyOrderSync\Model\ResourceModel\ShopifyOrder;
use Comave\ShopifyOrderSync\Model\ShopifyOrderFactory;
use Comave\ShopifyOrderSync\Helper\Shopify as ShopifyHelper;
use Magento\Framework\App\HttpRequestInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;
use Comave\MapOrderStatuses\Service\OrderStatuses;
use Comave\MapOrderStatuses\Model\ConfigProvider;

class Order implements WebhookTopicProcessorInterface
{
    /**
     * @param ShopifyOrderFactory $orderFactory
     * @param ShopifyOrder $resourceModel
     * @param LoggerInterface $logger
     * @param OrderRepositoryInterface $orderRepository
     * @param ShopifyHelper $shopifyHelper
     */
    public function __construct(
        private readonly ShopifyOrderFactory $orderFactory,
        private readonly ShopifyOrder $resourceModel,
        private readonly LoggerInterface $logger,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly ShopifyHelper $shopifyHelper,
        private readonly OrderStatuses $orderStatuses,
        private readonly ConfigProvider $orderStatusConfigProvider,
    ) {
    }

    /**
     * @param HttpRequestInterface $request
     * @return void
     * @throws InvalidWebhookRequestException
     */
    public function process(HttpRequestInterface $request): void
    {
        /** @var string|null $jsonParam */
        $jsonParam = $request->getContent();

        $this->logger->info(
            '[WebhookProcessor] Beginning order sync',
            [
                'body' => $jsonParam
            ]
        );

        $shopifyOrderId = $request->getHeader(
            WebhookValidatorInterface::ORDER_ID_HEADER,
            ''
        );

        if (!$shopifyOrderId) {
            $this->logger->warning(
                '[WebhookProcessor] Missing order ID header',
            );

            throw new InvalidWebhookRequestException(
                __('Order ID not found')
            );
        }

        $shopifyOrder = $this->orderFactory->create();
        $this->resourceModel->load($shopifyOrder, $shopifyOrderId, 'shopify_order_id');

        if (!$shopifyOrder->getId()) {
            $this->logger->warning(
                '[WebhookProcessor] Unidentified order ID provided',
                [
                    'shopifyOrderId' => $shopifyOrderId
                ]
            );

            throw new InvalidWebhookRequestException(
                __('Unable to identify shopify order')
            );
        }


        $shopId = $shopifyOrder->getShopId();

        // Fetch Shopify order status and tracking details
        $shopifyOrderStatus = $this->shopifyHelper->getOrderStatus($shopifyOrderId, $shopId);

        if ($this->orderStatuses->isStatusActive($shopifyOrderStatus) === false
            || $this->orderStatusConfigProvider->isCustomStatusMapped($shopifyOrderStatus) === false) {
            $this->logger->warning(
                'Skipped status update: Status is inactive or not permitted.',
                [
                    'order_id' => $shopifyOrderId,
                    'status' => $shopifyOrderStatus,
                ]
            );
            throw new InvalidWebhookRequestException(
                __('Status is inactive or not allowed.')
            );
        }

        $trackingData = $this->shopifyHelper->getTrackingDetails($shopifyOrderId, $shopId);
        $magentoOrderId = $shopifyOrder->getMagentoOrderId();
        $orderID = $this->shopifyHelper->getOrderIdByIncrementId($magentoOrderId);
        // Update tracking details if available
        if (!empty($trackingData[0]['tracking_numbers'])) {
            $trackingNumbers = $trackingData[0]['tracking_numbers'];
            $trackingCompany = $trackingData[0]['tracking_company'];
            $this->shopifyHelper->updateMagentoOrderTracking($orderID, $trackingNumbers, $trackingCompany);
        }
        // Load the Magento order and update its status
        $magentoOrder = $this->orderRepository->get($orderID);
        $magentoOrder->setStatus($shopifyOrderStatus);
        $this->orderRepository->save($magentoOrder);

        // Log success message
        $this->logger->info(
            '[WebhookProcessor] Successfully updated magento/shopify order',
            [
                'processor' => __CLASS__,
                'magentoId' => $magentoOrderId,
                'shopifyId' => $shopifyOrderId
            ]
        );
    }
}

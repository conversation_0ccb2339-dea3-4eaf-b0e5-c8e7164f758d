<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Controller\Seller;

use Comave\SellerPayouts\Helper\Data as PayoutHelper;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class Payouts extends Action
{
    /**
     * Constructor
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $_resultPageFactory
     * @param \Webkul\Marketplace\Helper\Data $_marketplaceHelper
     * @param \Comave\SellerPayouts\Helper\Data $_payoutHelper
     */
    public function __construct(
        Context $context,
        protected PageFactory $_resultPageFactory,
        protected MarketplaceHelper $_marketplaceHelper,
        protected PayoutHelper $_payoutHelper
    ) {
        parent::__construct($context);
    }

    /**
     * Execute Method
     *
     * @return \Magento\Framework\Controller\Result\Redirect|\Magento\Framework\View\Result\Page
     */
    public function execute(): \Magento\Framework\Controller\Result\Redirect|\Magento\Framework\View\Result\Page
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        /** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->_resultPageFactory->create();

        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('mpseller_layout2_payouts_manage');
        }

        $resultPage->getConfig()->getTitle()->set(__('Connect with stripe'));

        return $resultPage;
    }
}

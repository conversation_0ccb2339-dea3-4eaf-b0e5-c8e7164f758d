<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

// Verify seller Stripe account details are correct or not

namespace Comave\SellerPayouts\Controller\Seller;

use Comave\SellerPayouts\Helper\Data as StripeHelper;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\View\Result\PageFactory;
use Stripe\Account;

class Verify extends Action
{
    /**
     * Construct function
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Comave\SellerPayouts\Helper\Data $_stripeHelper
     * @param \Magento\Customer\Model\Session $_customerSession
     */
    public function __construct(
        Context $context,
        protected PageFactory $resultPageFactory,
        protected StripeHelper $_stripeHelper,
        protected CustomerSession $_customerSession
    ) {
        parent::__construct($context);
    }

    /**
     * Execute function
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute(): \Magento\Framework\Controller\Result\Redirect
    {
        $data = $this->getRequest()->getPostValue();
        $email = $data['stripe_account_email'];
        $country = $data['seller_stripe_country'];
        $name = $data['stripe_account_name'];

        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $verifiedAccount = $this->_stripeHelper->createSellerStripeAccount((string) $email, (string) $country);

        if (!$verifiedAccount || !isset($verifiedAccount['id'])) {
            return $this->handleAccountCreationFailure($resultRedirect);
        }

        return $this->handleSuccessfulAccountCreation($verifiedAccount, $name, $email, $country, $resultRedirect);
    }

    /**
     * Handle Successful Account Creation function
     *
     * @param \Stripe\Account|string $verifiedAccount
     * @param string $name
     * @param string $email
     * @param string $country
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleSuccessfulAccountCreation(
        Account|string $verifiedAccount,
        string $name,
        string $email,
        string $country,
        Redirect $resultRedirect
    ): Redirect {
        try {
            $customer = $this->_customerSession->getCustomer();
            $this->processCustomerAccount($customer, $verifiedAccount, $name, $email, $country);
            $accountLink = $this->createAccountLink($verifiedAccount);

            if (!$accountLink) {
                return $this->handleAccountLinkError($resultRedirect);
            }

            $this->handleSuccessMessage($accountLink);
            $resultRedirect->setPath('seller_payouts/seller/Payouts');
        } catch (\Exception $e) {
            $this->handleError($e, $resultRedirect);
        }

        return $resultRedirect;
    }

    /**
     * Process the customer account by deleting the existing Stripe account and updating customer data.
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @param \Stripe\Account|string $verifiedAccount
     * @param string $name
     * @param string $email
     * @param string $country
     * @return void
     */
    private function processCustomerAccount(
        Customer $customer,
        Account|string $verifiedAccount,
        string $name,
        string $email,
        string $country
    ): void {
        $this->deleteExistingStripeAccount($customer);
        $this->updateCustomerData($customer, $verifiedAccount['id'], $name, $email, $country);
    }

    /**
     * Create a Stripe account link for the verified account.
     *
     * @param \Stripe\Account|string $verifiedAccount
     * @return string|null
     */
    private function createAccountLink(Account|string $verifiedAccount): ?string
    {
        return $this->_stripeHelper->createStripeAccountLink((string) $verifiedAccount['id']);
    }

    /**
     * Handle the error case when the account link cannot be created.
     *
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleAccountLinkError(Redirect $resultRedirect): Redirect
    {
        $this->messageManager->addErrorMessage('There was a problem receiving the Stripe Verify Url.');

        return $resultRedirect->setPath('*/*/payouts');
    }

    /**
     * Display a success message with the account link for the user.
     *
     * @param string $accountLink
     * @return void
     */
    private function handleSuccessMessage(string $accountLink): void
    {
        $this->messageManager->addSuccess(
            __(
                'Congratulations! Your account is created on the ComAve Stripe platform. ' .
                'Please verify your account <a href="%1">here</a>.',
                $accountLink
            )
        );
    }

    /**
     * Handle errors that occur during the account creation process.
     *
     * @param \Exception $e
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return void
     */
    private function handleError(\Exception $e, Redirect $resultRedirect): void
    {
        $this->messageManager->addErrorMessage(
            __('There was a problem connecting your Stripe account: %1', $e->getMessage())
        );
        $resultRedirect->setPath('*/*/payouts');
    }

    /**
     * Handle Account Creation Failure function
     *
     * @param \Magento\Framework\Controller\Result\Redirect $resultRedirect
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    private function handleAccountCreationFailure(Redirect $resultRedirect): Redirect
    {
        $this->messageManager->addErrorMessage(
            'Failed to create Stripe account. Please check if your country is supported by Stripe.'
        );
        $resultRedirect->setPath('seller_payouts/seller/payouts');

        return $resultRedirect;
    }

    /**
     * Delete Existing Stripe Account function
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return void
     */
    private function deleteExistingStripeAccount(Customer $customer): void
    {
        $stripeClientId = $customer->getData('stripe_client_id');

        if (!empty($stripeClientId)) {
            $this->_stripeHelper->deleteSellerStripeAccount((string) $stripeClientId);
        }
    }

    /**
     * Update Customer Data function
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @param string $stripeClientId
     * @param string $name
     * @param string $email
     * @param string $country
     * @return void
     */
    private function updateCustomerData(
        Customer $customer,
        string $stripeClientId,
        string $name,
        string $email,
        string $country
    ): void {
        $customer->setData('stripe_client_id', $stripeClientId);
        $customer->setData('stripe_account_name', $name);
        $customer->setData('stripe_account_email', $email);
        $customer->setData('seller_stripe_country', $country);
        $customer->save();
    }
}

<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Service;

use Magento\Customer\Model\CustomerFactory;
use Comave\SellerPayouts\Helper\Data;
use Magento\Framework\App\Area;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class AdminNotificationServices
{
    private const STATUS_ACTIVE = 'active';
    private const STATUS_INACTIVE = 'disabled';
    private const STATUS_FUTURE = 'future_action';
    private const STATUS_ATTENTION = 'attention';
    private const EMAIL_TEMPLATE_IDENTIFIER = 'stripe_connect_accounts_status';

    private ?array $stripeAccounts = null;

    /**
     * @param Data $helper
     * @param CustomerFactory $customerModel
     * @param TransportBuilder $transportBuilder
     * @param CollectionFactory $customerCollectionFactory
     * @param StateInterface $inlineTranslation
     * @param MarketplaceHelper $marketplaceHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Data $helper,
        private readonly CustomerFactory $customerModel,
        private readonly TransportBuilder $transportBuilder,
        private readonly CollectionFactory $customerCollectionFactory,
        private readonly StateInterface $inlineTranslation,
        private readonly MarketplaceHelper $marketplaceHelper,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function stripeConnectionStatuses(): void
    {
        $stripeSecretKey = $this->helper->getDecryptedKey();
        $stripe = new \Stripe\StripeClient($stripeSecretKey);
        $linkedAccounts = $stripe->accounts->all();

        foreach ($linkedAccounts as $linkedAccount) {
            $issues = [];
            $issues['accountId'] = $linkedAccount->id;
            $issues['current'] = $linkedAccount->requirements['currently_due'];
            $issues['reason'] = $linkedAccount->requirements['disabled_reason'];
            $issues['future'] = $linkedAccount->requirements['eventually_due'];
            $this->sendAdminStripeEmail($issues);
        }
    }

    /**
     * @param $data
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    private function sendAdminStripeEmail($data): void
    {
        if ($this->getStatus($data) == self::STATUS_ACTIVE) {
            return;
        }
        $subject = __('Stripe account requires attention').'!';
        $sellerEmail = $this->getSellerEmailFromAccountId($data['accountId']);
        $message = 'Stripe '.__('account').' '. $data['accountId'] . __(' requires attention!').'<br />';
        if (!isset($sellerEmail['email'])) {
            $message .= __('Account not found in Magento sellers database').'<br />';
        }
        $message .= __('Currently due issues').' :<br />';
        foreach ($data['current'] as $issue) {
            $message .= $issue . '<br />';
        }
        $message .= __('Future due issues').' :<br />';
        foreach ($data['future'] as $issue) {
            $message .= $issue . '<br />';
        }
        $message .= '<br />';
        $this->send($subject, $message, $this->getStatus($data), $sellerEmail ?? null);
    }

    /**
     * @param $accountId
     * @return \Magento\Framework\DataObject|null
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getSellerEmailFromAccountId($accountId): ?\Magento\Framework\DataObject
    {
        if($this->stripeAccounts !== null) {
            return isset($this->stripeAccounts[$accountId]) ? $this->stripeAccounts[$accountId] : null;
        }
        $sellerCollection = $this->marketplaceHelper->getSellerCollection()->load();
        $sellerIds = [];
        foreach ($sellerCollection as $seller) {
            $sellerIds[] = $seller->getSellerId();
        }
        $customerEmail = $this->customerCollectionFactory->create()
            ->addAttributeToSelect('stripe_account_email')
            ->addAttributeToSelect('email')
            ->addAttributeToSelect('stripe_client_id')
            ->addAttributeToSelect('store_id')
            ->addFieldToFilter('entity_id', ['in' => $sellerIds])
            ->load();
        foreach ($customerEmail as $customer) {
            if($customer->getStripeClientId()){
                $this->stripeAccounts[$customer->getStripeClientId()] = $customer;
            }
        }

        return isset($this->stripeAccounts[$accountId]) ? $this->stripeAccounts[$accountId] : null;
    }

    /**
     * @param array $data
     * @return string
     */
    private function getStatus(array $data): string
    {
        return match (true) {
            isset($data['reason']) && $data['reason'] => self::STATUS_ATTENTION,
            !empty($data['current']) => self::STATUS_ATTENTION,
            !empty($data['future']) => self::STATUS_FUTURE,
            default => self::STATUS_ACTIVE,
        };
    }

    /**
     * @param string $subject
     * @param string $message
     * @param string $status
     * @param \Magento\Framework\DataObject|null $toSeller
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    public function send(string $subject, string $message, string $status = '',  null|\Magento\Framework\DataObject $toSeller = null): void
    {
        $sender = [
            'name' => 'Magento Onboard System',
            'email' => '<EMAIL>',
        ];
        $vars = [
            'subject' => $subject,
            'message' => $message,
            'status' => $status,
        ];

        $transporter = $this->transportBuilder->setTemplateIdentifier(
            self::EMAIL_TEMPLATE_IDENTIFIER
        )->setTemplateOptions(
            [
                'area' => Area::AREA_FRONTEND,
                'store' => Store::DEFAULT_STORE_ID,
            ]
        );

        $transporter->addTo($this->marketplaceHelper->getAdminEmailId());
        if ($toSeller) {
            $transporter->addTo($toSeller['email']);
            $transporter->setTemplateOptions(
                [
                    'area' => Area::AREA_FRONTEND,
                    'store' => $toSeller['store_id'],
                ]
            );
        }
        $transporter->setTemplateVars($vars)->setFromByScope($sender);
        $transport = $transporter->getTransport();

        try {
            $transport->sendMessage();
        } catch (\Exception $e) {
            $this->logger->critical('Cannot send stripe connect account check notification', [
                'message' => $e->getMessage(),
            ]);
        }
    }
}

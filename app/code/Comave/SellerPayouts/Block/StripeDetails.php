<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Block;

use Comave\Logger\Model\ComaveLogger;
use Comave\SellerPayouts\Helper\Data as PayoutHelper;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\Session;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\App\Http\Context;
use Magento\Framework\View\Element\Template\Context as TemplateContext;
use Webkul\Marketplace\Helper\Data as MpHelper;

class StripeDetails extends \Magento\Framework\View\Element\Template
{
    /**
     * Contruct function
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Comave\SellerPayouts\Helper\Data $payoutHelper
     * @param \Comave\Logger\Model\ComaveLogger $logger
     * @param \Magento\Framework\App\Http\Context $httpContext
     * @param mixed[] $data
     */
    public function __construct(
        TemplateContext $context,
        private MpHelper $mpHelper,
        private CustomerFactory $customerFactory,
        private CountryFactory $countryFactory,
        private Session $customerSession,
        private PayoutHelper $payoutHelper,
        private ComaveLogger $logger,
        private Context $httpContext,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * Get Customer function
     *
     * @return \Magento\Customer\Model\Customer|null
     */
    public function getCustomerDetails(): ?\Magento\Customer\Model\Customer
    {
        // Try to get customer from session
        $customer = $this->customerSession->getCustomer();

        if ($customer && $customer->getId()) {
            $this->logger->info('customer from session');

            return $customer;
        }

        // If session is not working, use httpContext
        $customerId = $this->httpContext->getValue('customer_id');

        if ($customerId) {
            $this->logger->info('customer from httpContext');

            return $this->customerFactory->create()->load($customerId);
        }

       // If session and is httpContext not working, use marketplace data
        $customerId = $this->mpHelper->getCustomerId();

        if ($customerId) {
            $this->logger->info('customer from marketplace');

            return $this->customerFactory->create()->load($customerId);
        }

        $this->logger->info('Empty');

        // If both methods fail, return null
        return null;
    }

    /**
     * Get Account Signin Link function
     *
     * @param string $stripeId
     * @return mixed|string|null
     */
    public function getAccountSigninlink(string $stripeId): mixed
    {
        return $this->payoutHelper->createStripeLoginLink($stripeId);
    }

    /**
     * Get Account Status function
     *
     * @param string $stripeId
     * @return bool
     */
    public function getAccountStatus(string $stripeId): bool
    {
        return $this->payoutHelper->getSellerAccountStatus($stripeId);
    }

    /**
     * Get Account Verification link function
     *
     * @param string $stripeId
     * @return mixed|bool|string|null
     */
    public function getAccountverificationlink(string $stripeId): mixed
    {
         return $this->payoutHelper->createStripeAccountLink($stripeId);
    }

    /**
     * Get Country Options function
     *
     * @return mixed[]
     */
    public function getCountryOptions(): array
    {
        $countryCollection = $this->countryFactory->create()->getCollection();
        $countries = [];

        foreach ($countryCollection as $country) {
            $countries[$country->getCountryId()] = $country->getName();
        }

        return $countries;
    }
}

<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package Webkul_MpMultiShopifyStoreMageConnect
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
    $categoriesList = [];
    $data = $block->helper->getAccountDetails();
    $attributeSetList = $block->listAttributeSet();
    $attributeSets = [];
if ($attributeSetList) {
    foreach ($attributeSetList->getItems() as $list) {
        $attributeSets[] = $list->getData();
    }
}
    $categorySets = [];
    $attributeCats = $block->listCategories();
if ($attributeCats) {
    foreach ($attributeCats as $cats) {
        $categorySets[] = $cats;
    }
}
    $storeViews = [];
    $stores = $block->getStores();
foreach ($stores as $store) {
    if ($store["code"] != "admin") {
        $storeViews[] = $store->getData();
    }
}
    $imageImportOptions = $block->getImageImportOptions();
    $impOptionTypes = $block->optionTypes();
    $priceRuleOptions = $block->priceRuleOptions();
    $orderStatus = $block->orderStatus();
    $baseCurrCode = $block->baseCurrCode();
    $qtyHint = '<small>'.__('(Default qty will be used in case qty is 0 while export)').'</small>';
    $productTypes = $block->getProductOptions();
    $allowedProductTypes = $data['product_type_allowed'];
    $eachAllowedType = explode(",", $allowedProductTypes);
foreach ($eachAllowedType as $type) {
    if ($type!="") {
        $categoriesList[] = $type;
    }
}
    $templates = $block->getTemplates();
?>
    <form method="post" action="<?= /* @noEscape */ $block->getUrl('mpmultishopifystoremageconnect/account/save'); ?>" 
    enctype="multipart/form-data" data-mage-init='{"validation":{}}' id="wk_ssp_add_form">
        <fieldset class="fieldset">
            <legend class="legend wk_ssp_set_width">
                <span><?= /* @noEscape */ __("Add Account"); ?></span>
                <button class="button wk-mp-btn" type="submit" id="wk_ssp_save_button">
                    <span><span><?= /* @noEscape */ __("Save Account"); ?></span></span>
                </button>
            </legend>
            <div class="field required">
                <label for="store_name" class="label">
                    <span><?= /* @noEscape */ __("Store Name"); ?></span>
                </label>
                <div class="control">
        <input type="text" data-validate="{required:true}" class="input-text required-entry" 
        title="Store Name" name="store_name" id="store_name" aria-required="true" 
        value="<?= /* @noEscape */ $data['store_name']; ?>" <?php if ($data["store_name"]!="") { echo "disabled";} ?>>
                </div>
            </div>
            <div class="field required">
                <label for="attribute_set_id" class="label">
                    <span><?= /* @noEscape */ __("Attribute Set"); ?></span>
                </label>
        <div class="control">
    <select id="attribute_set_id" name="attribute_set_id" data-validate="{required:true}" 
    class="input-text required-entry" title="Attribute Set" aria-required="true">
        <?php foreach ($attributeSets as $attributeSet) { ?>
            <option <?php if ($data["attribute_set_id"]==$attributeSet["attribute_set_id"]) { echo "selected";} ?> 
            value='<?=/* @noEscape */ $attributeSet["attribute_set_id"]?>'>
                <?=/* @noEscape */$attributeSet["attribute_set_name"]?>
            </option>
        <?php } ?>
    </select>
        </div>
            </div>

            <div class="field required">
                <label for="shopify_app_select" class="label">
                   <span><?= /* @noEscape */ __("Use Custom App"); ?></span>
                </label>
                <div class="control">
                    <select id="shopify_app_select" name="shopify_app_select" data-validate="{required:true}" 
                        class="input-text required-entry" title="Use Custom App" aria-required="true">
                        <option <?php if ($data["shopify_app_select"]==0) { echo "selected";} ?> 
                         value="0">No</option>
                        <option <?php if ($data["shopify_app_select"]==1) { echo "selected";} ?>
                         value="1">Yes</option>
                    </select>
                </div>
            </div>
            
            <?php if ($data["shopify_app_select"]==1) { ?>
                <div class="field required" id="shopify_custom_app">
                    <label for="access_token" class="label">
                    <span><?= /* @noEscape */ __("Access Token"); ?></span>
                    </label>
                    <div class="control">
                        <input id="access_token" name="access_token" 
                            data-validate="{required:true}" value='<?=/* @noEscape */ $data["access_token"]?>' 
                            title="access_token" type="password" class="input-text required-entry" aria-required="true">
                    </div>
                </div>
                <div id="shopify_private_app_key"></div>
                <div id="shopify_private_app_pwd"></div>
            <?php } else {?>
                <div id="shopify_custom_app"></div>
                <div class="field required" id="shopify_private_app_key">
                <label for="shopify_api_key" class="label">
                    <span><?= /* @noEscape */ __("Api Key"); ?></span>
                </label>
               <div class="control">
                <input id="shopify_api_key" name="shopify_api_key" 
                data-validate="{required:true}" value='<?=/* @noEscape */ $data["shopify_api_key"]?>' 
                title="API Key" type="password" class="input-text required-entry" aria-required="true">
                </div>
                    </div>
                    <div class="field required" id="shopify_private_app_pwd">
                        <label for="shopify_pwd" class="label">
                            <span><?= /* @noEscape */ __("Paasword"); ?></span>
                        </label>
                        <div class="control">
                        <input id="shopify_pwd" name="shopify_pwd" 
                        data-validate="{required:true}" value='<?=/* @noEscape */ $data["shopify_pwd"]?>' 
                        title="Paasword" type="password" class="input-text required-entry" aria-required="true">
                        </div>
                    </div>   
            <?php } ?>
            
          
            <div class="field required">
                <label for="shopify_domain_name" class="label">
                    <span><?= /* @noEscape */ __("Domian Name"); ?></span>
                </label>
                <div class="control">
                    <input type="text" data-validate="{required:true}" class="input-text required-entry" 
                    title="Domian Name" name="shopify_domain_name" id="shopify_domain_name" aria-required="true" 
                    value="<?= /* @noEscape */ $data['shopify_domain_name']; ?>">
                </div>
            </div>
            <div class="field required">
                <label for="default_cate" class="label">
                    <span><?= /* @noEscape */ __("Default Category"); ?></span>
                </label>
        <div class="control">
            <select id="default_cate" name="default_cate" data-validate="{required:true}" 
            class="input-text required-entry" title="Default Category" aria-required="true">
                <?php foreach ($attributeCats as $attributeCat) { ?>
                    <option <?php if ($data["default_cate"]==$attributeCat["value"]) { echo "selected";} ?> 
                    value='<?=/* @noEscape */ $attributeCat["value"]?>'><?=/* @noEscape */ $attributeCat["label"]?>
                    </option>
                <?php } ?>
            </select>
        </div>
            </div>
            <div class="field required">
                <label for="default_store_view" class="label">
                    <span><?= /* @noEscape */ __("Default Store Views"); ?></span>
                </label>
        <div class="control">
            <select id="default_store_view" name="default_store_view" data-validate="{required:true}" 
            class="input-text required-entry" title="Default Store Views" aria-required="true">
                <?php foreach ($storeViews as $storeView) { ?>
                    <option <?php if ($data["default_store_view"]==$storeView["store_id"]) { echo "selected";} ?> 
                    value='<?=/* @noEscape */ $storeView["store_id"]?>'><?=/* @noEscape */ $storeView["name"]?>
                    </option>
                <?php } ?>
            </select>
        </div>
            </div>
            <div class="field required">
                <label for="import_product" class="label">
                    <span><?= /* @noEscape */ __("Import Product"); ?></span>
                </label>
    <div class="control">
        <select id="import_product" name="import_product" data-validate="{required:true}" 
        class="input-text required-entry" title="Import Product" aria-required="true">
            <?php foreach ($impOptionTypes as $imports) { ?>
                <option <?php if ($data["import_product"]==$imports["value"]) { echo "selected";} ?> 
                value='<?=/* @noEscape */ $imports["value"]?>'><?=/* @noEscape */ $imports["label"]?></option>
            <?php } ?>
        </select>
    </div>
            </div>
            <div class="field required">
                <label for="item_with_html" class="label">
                    <span><?= /* @noEscape */ __("Product Description With HTML"); ?></span>
                </label>
                <div class="control">
                    <select id="item_with_html" name="item_with_html" data-validate="{required:true}" 
                    class="input-text required-entry" title="Product Description With HTML" aria-required="true">
                        <option <?php if ($data["item_with_html"]=="1") { echo "selected";} ?> 
                        value='1'><?=/* @noEscape */ __('Yes')?></option>
                        <option <?php if ($data["item_with_html"]=="0") { echo "selected";} ?> 
                        value='0'><?=/* @noEscape */ __('No')?></option>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label for="price_rule_on" class="label">
                    <span><?= /* @noEscape */ __("Price Rule Applicable For"); ?></span>
                </label>
    <div class="control">
        <select id="price_rule_on" name="price_rule_on" data-validate="{required:true}" 
        class="input-text required-entry" title="Price Rule Applicable For" aria-required="true">
        <?php foreach ($priceRuleOptions as $options) { ?>
                <option <?php if ($data["price_rule_on"]==$options["value"]) { echo "selected";} ?> 
                value='<?=/* @noEscape */ $options["value"]?>'><?=/* @noEscape */ $options["label"]?></option>
            <?php } ?>
        </select>
    </div>
            </div>

            <div class="field required">
                <label for="order_update_webhook" class="label">
                    <span><?= /* @noEscape */ __("Order update by webhook"); ?></span>
                </label>
                <div class="control">
                    <select id="order_update_webhook" name="order_update_webhook" data-validate="{required:true}" 
                    class="input-text required-entry" title="Order update by webhook" aria-required="true">
                        <option <?php if ($data["order_update_webhook"]=="1") { echo "selected";} ?> 
                        value='1'><?=/* @noEscape */ __('Yes')?></option>
                        <option <?php if ($data["order_update_webhook"]=="0") { echo "selected";} ?> 
                        value='0'><?=/* @noEscape */ __('No')?></option>
                    </select>
                </div>
            </div>


            <div class="field required">
                <label for="product_update_webhook" class="label">
                    <span><?= /* @noEscape */ __("Product update by webhook"); ?></span>
                </label>
                <div class="control">
                    <select id="product_update_webhook" name="product_update_webhook" data-validate="{required:true}" 
                    class="input-text required-entry" title="Product update by webhook" aria-required="true">
                        <option <?php if ($data["product_update_webhook"]=="1") { echo "selected";} ?> 
                        value='1'><?=/* @noEscape */ __('Yes')?></option>
                        <option <?php if ($data["product_update_webhook"]=="0") { echo "selected";} ?> 
                        value='0'><?=/* @noEscape */ __('No')?></option>
                    </select>
                </div>
            </div>

            <div class="field">
                <label for="webhook_verify_key" class="label">
                    <span><?= /* @noEscape */ __("Webhook Verification Key"); ?></span>
                </label>
                <div class="control">
                <input type="text" class="input-text" 
                title="Webhook Verification Key" name="webhook_verify_key" id="webhook_verify_key" aria-required="true" 
                value="<?= /* @noEscape */ $data['webhook_verify_key']; ?>">
                </div>
            </div>

            <div class="field required">
                <label for="order_status" class="label">
                    <span><?= /* @noEscape */ __("Order Status"); ?></span>
                </label>
        <div class="control">
            <select id="order_status" name="order_status" data-validate="{required:true}" 
            class="input-text required-entry" title="Order Status" aria-required="true">
                <?php foreach ($orderStatus as $options) { ?>
                    <option <?php if ($data["order_status"]==$options["value"]) { echo "selected";} ?> 
                    value='<?=/* @noEscape */ $options["value"]?>'><?=/* @noEscape */ $options["label"]?></option>
                <?php } ?>
            </select>
        </div>
            </div>
            <div class="field required">
                <label for="currency_conv_rate" class="label">
                    <span><?= /* @noEscape */ __("Currency Convertion Rate"); ?></span>
                </label>
                <div class="control">
                <input id="currency_conv_rate" name="currency_conv_rate" 
                data-validate="{required:true}" value='<?=/* @noEscape */ $data["currency_conv_rate"]?>' 
                title="Currency Convertion Rate" type="number" 
                class="required-entry validate-number validate-zero-or-greater" aria-required="true">
                </div>
            </div>
            <div class="field required">
                <label for="default_qty" class="label">
                    <span><?= /* @noEscape */ __("Default Product Quantity ".$qtyHint); ?></span>
                </label>
                <div class="control">
                <input id="default_qty" name="default_qty" 
                data-validate="{required:true}" value='<?=/* @noEscape */ $data["default_qty"]?>' 
                title="Default Product Quantity" type="number" 
                class="required-entry validate-number validate-zero-or-greater" aria-required="true">
                </div>
            </div>
            <div class="field">
                <label for="template_id" class="label">
                    <span><?= /* @noEscape */ __("Select Template "); ?></span>
                </label>
    <div class="control">
        <select id="template_id" name="template_id" 
        class="input-text" title="Select Template">
            <?php foreach ($templates as $options) { ?>
                <option <?php if ($data["template_id"]==$options["value"]) { echo "selected";} ?> 
                value='<?=/* @noEscape */ $options["value"]?>'><?=/* @noEscape */ $options["label"]?></option>
            <?php } ?>
        </select>
    </div>
            </div>
            <div class="field required">
                <label for="product_type_allowed" class="label">
                    <span><?= /* @noEscape */ __("Product Type For Export"); ?></span>
                </label>
    <div class="control">
        <select id="product_type_allowed" name="product_type_allowed[]" data-validate="{required:true}" 
        class="input-text" title="Product Type For Export" aria-required="true" size="4" multiple="multiple">
            <?php foreach ($productTypes as $options) { ?>
                <option <?php if (in_array($options["value"], $categoriesList)) { echo "selected";} ?> 
                value='<?=/* @noEscape */ $options["value"]?>'><?=/* @noEscape */ $options["label"]?></option>
            <?php } ?>
        </select>
    </div>
            </div>
            <div class="field required">
                <label for="product_images_import_with" class="label">
                    <span><?= /* @noEscape */ __("Choose Product Images Import Method"); ?></span>
                </label>
                <div class="control">
                    <select id="product_images_import_with" name="product_images_import_with" data-validate="{required:true}" 
                    class="input-text" title="Choose Product Images Import Method" aria-required="true">
                    <?php foreach ($imageImportOptions as $options) { ?>
                        <option <?php if ($data["product_images_import_with"]==$options["value"]) { echo "selected";} ?> 
                        value='<?=/* @noEscape */ $options["value"]?>'><?=/* @noEscape */ $options["label"]?></option>
                    <?php } ?>
                    </select>
                </div>
            </div>
            <div class="field required">
                <label for="other_info" class="label">
                    <span><?= /* @noEscape */ __("Other Information"); ?></span>
                </label>
                <div class="control">
                <textarea id="other_info" name="other_info" data-validate="{required:true}"  
                title="Other Information" type="text" class="required-entry input-text" 
                aria-required="true"><?=/* @noEscape */ $data["other_info"]?></textarea>
                </div>
            </div>
            <?php
            if (array_key_exists("entity_id", $data)) {?>
                    <input name="id" value='<?=/* @noEscape */ $data["entity_id"] ?>' type="hidden"/>
            <?php }
            ?>
            <input name="front" value='1' type="hidden"/>
        </fieldset>
    </form>

    <script>
        require([
                'jquery'
        ],
        function ($) {
                $("#shopify_app_select").on('change', function() {
                  var shopify_app_select =  this.value;
                  if (shopify_app_select==1) {
                    $("#shopify_private_app_key").removeClass("field required");
                    $("#shopify_private_app_pwd").removeClass("field required");
                    $("#shopify_private_app_key").empty();
                    $("#shopify_private_app_pwd").empty();
                    $("#shopify_custom_app").addClass("field required");
                    $("#shopify_custom_app").empty();
                    $("#shopify_custom_app").append('<label for="access_token" class="label"><span>'
                    +"<?= /* @noEscape */ __('Access Token'); ?>"
                    +'</span></label><div class="control"><input id="access_token" name="access_token"'
                    +'data-validate="{required:true}" value="'
                    +"<?=/* @noEscape */ $data['access_token']?>"
                    +'" title="access_token" type="password" class="input-text required-entry"'+
                    'aria-required="true"></div>');
                  } else {
                    $("#shopify_custom_app").removeClass("field required");
                    $("#shopify_custom_app").empty();
                    $("#shopify_private_app_key").addClass("field required");
                    $("#shopify_private_app_pwd").addClass("field required");
                    $("#shopify_private_app_key").empty();
                    $("#shopify_private_app_key").append('<label for="shopify_api_key" class="label"><span>'
                    +"<?= /* @noEscape */ __('Api Key'); ?>"
                    +'</span></label><div class="control"><input id="shopify_api_key" name="shopify_api_key"'
                    +'data-validate="{required:true}" value="'
                    +"<?=/* @noEscape */ $data['shopify_api_key']?>"
                    +'" title="Api Key" type="password" class="input-text required-entry" aria-required="true"></div>');
                    $("#shopify_private_app_pwd").empty();
                    $("#shopify_private_app_pwd").append('<label for="shopify_pwd" class="label"><span>'
                    +"<?= /* @noEscape */ __('Paasword'); ?>"
                    +'</span></label><div class="control"><input id="shopify_pwd" name="shopify_pwd"'
                    +'data-validate="{required:true}" value="'
                    +"<?=/* @noEscape */ $data['shopify_pwd']?>"
                    +'" title="Paasword" type="password" class="input-text required-entry"'
                    +'aria-required="true"> </div>');
                      
                  }
                });
        });
    </script>
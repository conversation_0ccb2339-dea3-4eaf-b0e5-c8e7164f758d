<?php

declare(strict_types=1);

namespace Comave\SellerApi\Model\Queue\Consumer;

use Magento\Catalog\Api\Data\ProductAttributeMediaGalleryEntryInterface;
use Magento\Catalog\Api\Data\ProductAttributeMediaGalleryEntryInterfaceFactory;
use Magento\Catalog\Api\ProductAttributeMediaGalleryManagementInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Api\Data\ImageContentInterface;
use Magento\Framework\Api\Data\ImageContentInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Phrase;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class HandleProductsMedia
{
    public const string TOPIC_NAME = 'seller.product.media.sync';

    /**
     * @param ProductRepositoryInterface $productRepository
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     * @param DataObjectHelper $dataObjectHelper
     * @param ImageContentInterfaceFactory $imageContentFactory
     * @param ProductAttributeMediaGalleryManagementInterface $productAttributeMediaGalleryManagement
     * @param ProductAttributeMediaGalleryEntryInterfaceFactory $galleryEntryFactory
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger,
        private readonly DataObjectHelper $dataObjectHelper,
        private readonly ImageContentInterfaceFactory $imageContentFactory,
        private readonly ProductAttributeMediaGalleryManagementInterface $productAttributeMediaGalleryManagement,
        private readonly ProductAttributeMediaGalleryEntryInterfaceFactory $galleryEntryFactory,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * @param string $mediaJson
     * @return void
     */
    public function execute(string $mediaJson): void
    {
        try {
            $unserialisedData = $this->serializer->unserialize($mediaJson);
            if (!isset($unserialisedData['sku']) || !isset($unserialisedData['media_gallery_entries'])) {
                $this->logger->info(
                    '[ProductSyncMediaGallery] Queue message invalid, missing sku / entries',
                    [
                        'message' => $unserialisedData
                    ]
                );

                return;
            }

            $this->logger->info(
                '[ProductSyncMediaGallery] Beginning process for',
                [
                    'product' => $unserialisedData['sku'],
                    'imageCount' => count($unserialisedData['media_gallery_entries'])
                ]
            );
            $this->storeManager->setCurrentStore(0);
            $start = microtime(true);
            $this->removeMediaGallery($unserialisedData['sku']);

            /** @var string $mediaGalleryEntry */
            foreach ($unserialisedData['media_gallery_entries'] as $mediaGalleryEntry) {
                try {
                    /** @var ProductAttributeMediaGalleryEntryInterface $mediaGalleryEntryObj */
                    $mediaGalleryEntryObj = $this->galleryEntryFactory->create();
                    $this->dataObjectHelper->populateWithArray(
                        $mediaGalleryEntryObj,
                        $this->getImageArr(stripslashes($mediaGalleryEntry)),
                        ProductAttributeMediaGalleryEntryInterface::class
                    );
                    $this->productAttributeMediaGalleryManagement->create(
                        $unserialisedData['sku'],
                        $mediaGalleryEntryObj
                    );
                } catch (\Exception $e) {
                    $this->logger->error(
                        '[ProductMediaSyncError] Unable to process image for sku',
                        [
                            'message' => $e->getMessage(),
                            'sku' =>  $unserialisedData['sku']
                        ]
                    );

                    continue;
                }
            }

            $end = microtime(true);
            $this->logger->info(
                '[ProductSyncMediaGallery] Finished process for',
                [
                    'product' => $unserialisedData['sku'],
                    'imageCount' => count($unserialisedData['media_gallery_entries']),
                    'executionTime' => number_format($end - $start, 2)
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[ProductMediaSyncError] Unable to process images',
                [
                    'message' => $e->getMessage(),
                ]
            );
        }
    }

    /**
     * @param string $mediaGalleryEntry
     * @return array
     * @throws InputException
     */
    private function getImageArr(string $mediaGalleryEntry): array
    {
        $imageContents = file_get_contents($mediaGalleryEntry);
        $imageProperties = @getimagesizefromstring($imageContents);
        if (empty($imageProperties)) {
            throw new InputException(new Phrase('The image content must be valid base64 encoded data.'));
        }

        $imagePathInfo = pathinfo($mediaGalleryEntry);
        /** @var ImageContentInterface $imageContent */
        $imageContent = $this->imageContentFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $imageContent,
            [
                'base64_encoded_data' => base64_encode($imageContents),
                'type' => $imageProperties['mime'],
                'name' => strtolower(
                    preg_replace(
                        "/[^a-zA-Z0-9]/",
                        "",
                        $imagePathInfo['basename']
                    )
                )
            ],
            ImageContentInterface::class
        );
        return [
            'media_type' => 'image',
            'label' => 'Import Image',
            'position' => 1,
            'disabled' => false,
            'types' => ['image', 'small_image', 'thumbnail', 'swatch_image'],
            'content' => $imageContent
        ];
    }

    /**
     * @param string $sku
     * @return void
     */
    private function removeMediaGallery(string $sku): void
    {
        try {
            $this->logger->info(
                '[ProductSyncMediaGallery] Removing existing media gallery',
                [
                    'product' => $sku,
                ]
            );
            $product = $this->productRepository->get($sku);

            foreach ($product->getMediaGalleryEntries() as $entry) {
                $this->productAttributeMediaGalleryManagement->remove(
                    $sku,
                    $entry->getId()
                );
            }

            $this->logger->info(
                '[ProductSyncMediaGallery] Finished removing existing media gallery',
                [
                    'product' => $sku,
                ]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                '[ProductSyncMediaGallery] Removing existing media gallery failed',
                [
                    'product' => $sku,
                    'exception' => $e->getMessage(),
                ]
            );
        }
    }
}

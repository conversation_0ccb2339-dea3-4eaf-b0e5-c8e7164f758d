<?xml version="1.0"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         extends="true">
    <columns name="marketplace_sellers_columns">
        <column name="integration_type">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Comave\SellerApi\Model\Option\Source\IntegrationsType</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="editor" xsi:type="string">select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Integration type</item>
                    <item name="sortOrder" xsi:type="number">500</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>
